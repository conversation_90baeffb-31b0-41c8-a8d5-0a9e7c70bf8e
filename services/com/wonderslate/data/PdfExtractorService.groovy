package com.wonderslate.data

import com.wonderslate.log.McqExtractLog
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.apache.commons.io.FileUtils
import org.springframework.web.multipart.MultipartFile

@Transactional
class PdfExtractorService {
    PromptService promptService
    def springSecurityService
    UtilService utilService
    def grailsApplication

    def serviceMethod() {

    }

    def processPdfImages(Long bookId, Long chapterId, Long resId, List<MultipartFile> imageFiles, boolean isQuizImages, boolean isExtractedImages, List<MultipartFile> croppedImages) {
        try {
            String uploadParentDir = "supload"
            def baseDir = "${uploadParentDir}/pdfextracts/${bookId}/${chapterId}/${resId}"
            def imageDir = isQuizImages ? "${baseDir}/extractedQuizImages" : "${baseDir}/full_images"

            // Ensure image directory exists
            File imageUploadDir = new File(imageDir)
            if (!imageUploadDir.exists()) {
                imageUploadDir.mkdirs()
            }

            // Store croppedImages in page-specific folders
            def pageFolders = [:]

            croppedImages.each { MultipartFile file ->
                if (!file.empty) {
                    String filename = file.originalFilename
                    def pageMatcher = filename =~ /(page_\d+)/

                    if (pageMatcher.find()) {
                        String pageFolder = pageMatcher.group(1)
                        File pageDir = new File(baseDir, pageFolder) // Store cropped images in baseDir/page_X
                        if (!pageDir.exists()) {
                            pageDir.mkdirs()
                            pageFolders[pageFolder] = true
                        }

                        FileUtils.writeByteArrayToFile(new File(pageDir, filename), file.bytes)
                    } else {
                        FileUtils.writeByteArrayToFile(new File(baseDir, filename), file.bytes)
                    }
                }
            }

            // Store imageFiles in the appropriate folder (extractedQuizImages or fullImages)
            imageFiles.each { MultipartFile file ->
                if (!file.empty) {
                    String filename = file.originalFilename
                    FileUtils.writeByteArrayToFile(new File(imageUploadDir, filename), file.bytes)
                }
            }

            def images = getImagePaths(bookId, chapterId, resId, isQuizImages, isExtractedImages)
            return [
                    success: true,
                    message: "Files uploaded successfully",
                    data: [
                            bookId: bookId,
                            totalFiles: imageFiles.size(),
                            images: images["data"]["images"],
                            croppedImages: images["data"]["croppedImages"],
                            extractedImages: images["data"]["extractedImages"]
                    ]
            ]
        } catch (Exception e) {
            log.error("Error processing PDF images: ${e.message}", e)
            return [success: false, message: e.message]
        }
    }

    def getImagePaths(Long bookId, Long chapterId, Long resId, boolean isQuizImages, boolean isExtractedImages) {
        try {
            // Define the base directory path
            String uploadParentDir = "supload"
            def baseDir = "${uploadParentDir}/pdfextracts/${bookId}/${chapterId}/${resId}"
            def fullImagesDir = "${baseDir}/full_images"
            def quizImagesDir = "${baseDir}/extractedQuizImages"

            File directory = new File(baseDir)
            File fullImagesDirectory = new File(fullImagesDir)
            File quizImagesDirectory = new File(quizImagesDir)

            if (!directory.exists()) {
                return [
                        success: false,
                        message: "Directory not found for bookId: ${bookId}"
                ]
            }

            // Base URL for image links
            def baseUrl = "/funlearn"

            // Lists to store image URLs
            def imageLinks = []
            def croppedImages = []
            def extractedImages = []

            // Retrieve cropped images from page-specific folders
            directory.listFiles()?.each { File file ->
                if (file.isDirectory() && file.name.startsWith('page_')) {
                    file.listFiles()?.each { File imageFile ->
                        if (imageFile.isFile() && imageFile.name.toLowerCase().endsWith('.png')) {
                            def relativePath = "${baseDir}/${file.name}/${imageFile.name}"
                            def imageUrl = "${baseUrl}/downloadEpubImage?source=${relativePath}"
                            croppedImages << imageUrl
                        }
                    }
                }
            }

            // Retrieve full images
            if (fullImagesDirectory.exists()) {
                fullImagesDirectory.listFiles()?.each { File imageFile ->
                    if (imageFile.isFile() && imageFile.name.toLowerCase().endsWith('.png')) {
                        def relativePath = "${fullImagesDir}/${imageFile.name}"
                        def imageUrl = "${baseUrl}/downloadEpubImage?source=${relativePath}"
                        imageLinks << imageUrl  // Always store in imageLinks
                    }
                }
            }

            // Retrieve extracted quiz images if isQuizImages is true
            if (isQuizImages && quizImagesDirectory.exists()) {
                quizImagesDirectory.listFiles()?.each { File imageFile ->
                    if (imageFile.isFile() && imageFile.name.toLowerCase().endsWith('.png')) {
                        def relativePath = "${quizImagesDir}/${imageFile.name}"
                        def imageUrl = "${baseUrl}/downloadEpubImage?source=${relativePath}"
                        extractedImages << imageUrl  // Store in extractedImages
                    }
                }
            }

            // Return the structured response
            return [
                    success: true,
                    data: [
                            bookId: bookId,
                            imageCount: imageLinks.size() + extractedImages.size(),
                            images: imageLinks, // Always return full images
                            croppedImages: croppedImages,
                            extractedImages: extractedImages // Only populated if isQuizImages is true
                    ]
            ]

        } catch (Exception e) {
            log.error("Error getting image paths: ${e.message}", e)
            return [success: false, message: e.message]
        }
    }

    def fetchQuestions(def request, String apiEndpoint, def requestBody) {
        String baseUrl = promptService.getGPTServerUrl(request) + "/" + apiEndpoint
        URL url = new URL(baseUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Write the JSON body - directly pass the complete requestBody
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            def json = new JsonBuilder(requestBody)  // Use the entire requestBody
            writer.write(json.toString())
            writer.flush()
            writer.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                        success: true,
                        data: jsonResponse,
                        message: "MCQs extracted successfully"
                ]
            } else {
                // Read the error response body to get more details
                def reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                ))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                return [
                        success: false,
                        data: null,
                        message: "Error from server: ${responseCode}. Details: ${response.toString()}"
                ]
            }
        } catch (Exception e) {
            log.error("Error fetching questions: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def startExtraction(def request, String resId, boolean showProcessStatus = true, String username = null) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/extract"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            // Create a log entry with 'started' status
            McqExtractLog logEntry = new McqExtractLog(
                username: username,
                resId: resId ? Long.parseLong(resId) : null,
                startTime: new Date(),
                status: 'started'
            )
            logEntry.save(flush: true, failOnError: true)

            // Make the API call
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Write the JSON body
            def requestBody = [
                resId: resId,
                showProcessStatus: showProcessStatus
            ]

            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            def json = new JsonBuilder(requestBody)
            writer.write(json.toString())
            writer.flush()
            writer.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())

                // Get the task ID from the response
                String taskId = jsonResponse.task_id

                // Update the log entry with the task ID from the response
                logEntry.taskId = taskId
                logEntry.status = 'in_progress'
                logEntry.save(flush: true)

                return taskId
            } else {
                // Read the error response body to get more details
                def reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                ))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                logEntry.status = 'error'
                logEntry.endTime = new Date()
                logEntry.save(flush: true)

                throw new Exception("Error from server: ${responseCode}. Details: ${response.toString()}")
            }

        } catch (Exception e) {
            log.error("Error starting extraction: ${e.message}", e)
            throw e
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def getTaskStatus(def request, String taskId) {
        try {
            // Find the log entry for this task
            McqExtractLog logEntry = McqExtractLog.findByTaskId(taskId)

            // If not found by taskId, try to find by other means
            if (!logEntry) {
                // Try to find by resId
                try {
                    Long resIdLong = Long.parseLong(taskId)
                    logEntry = McqExtractLog.findByResId(resIdLong)
                } catch (NumberFormatException e) {
                    // taskId is not a valid Long, so we can't find by resId
                }

                // If still not found, get the most recent extraction log
                if (!logEntry) {
                    logEntry = McqExtractLog.list(sort: 'startTime', order: 'desc', max: 1)[0]
                }
            }

            if (!logEntry) {
                return [success: false, status: 'not_found', message: "Task ID not found"]
            }

            // Always check the API for the latest status
            // This ensures we get real-time updates even if the database status hasn't changed
            String apiUrl = promptService.getGPTServerUrl(request) + "/task/" + taskId + "/status"
            URL url = new URL(apiUrl)
            HttpURLConnection connection = null

            try {
                connection = (HttpURLConnection) url.openConnection()
                connection.setRequestMethod("GET")
                connection.setRequestProperty("Content-Type", "application/json")
                connection.setRequestProperty("Accept", "application/json")
                connection.setDoOutput(false)
                connection.setDoInput(true)

                // No request body needed for GET request

                def responseCode = connection.getResponseCode()

                // Read response
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                    def response = new StringBuilder()
                    String line
                    while ((line = reader.readLine()) != null) {
                        response.append(line)
                    }
                    reader.close()

                    def jsonSlurper = new JsonSlurper()
                    def jsonResponse = jsonSlurper.parseText(response.toString())

                    // Update the log entry with the latest status
                    if (jsonResponse.status == 'completed') {
                        logEntry.status = 'completed'
                        logEntry.endTime = new Date()
                        logEntry.save(flush: true)
                    } else if (jsonResponse.status == 'failed') {
                        logEntry.status = 'error'
                        logEntry.endTime = new Date()
                        logEntry.save(flush: true)
                    } else {
                        logEntry.status = jsonResponse.status ?: 'in_progress'
                        logEntry.save(flush: true)
                    }

                    // Extract any steps or messages from the response
                    def steps = jsonResponse.steps ?: []
                    def currentStep = jsonResponse.current_step ?: jsonResponse.message ?: ""

                    // Normalize progress value if needed
                    def progress = jsonResponse.progress ?: 0

                    // Log the progress value for debugging
                    log.info("Progress value from API: ${progress}")

                    // Check for data in different possible locations
                    def resultData = null
                    if (jsonResponse.result) {
                        resultData = jsonResponse.result
                    } else if (jsonResponse.data) {
                        resultData = jsonResponse.data
                    }

                    // If status is completed but no data, try to get data from other sources
                    if (logEntry.status == 'completed' && !resultData) {
                        log.info("Status is completed but no data found in API response")
                    }

                    return [
                        success: true,
                        status: logEntry.status,
                        progress: progress,
                        message: currentStep,
                        steps: steps,
                        data: resultData,
                        result: resultData  // Include data in both fields for compatibility
                    ]
                } else {
                    // Read the error response body to get more details
                    def reader = new BufferedReader(new InputStreamReader(
                            responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                    ))
                    def response = new StringBuilder()
                    String line
                    while ((line = reader.readLine()) != null) {
                        response.append(line)
                    }
                    reader.close()

                    log.warn("API returned non-OK status: ${responseCode}. Details: ${response.toString()}")

                    // Even if the API call fails, return the current status from the database
                    // with a warning message
                    return [
                        success: true,
                        status: logEntry.status,
                        message: "Checking status... (API returned: ${responseCode})",
                        progress: 0
                    ]
                }
            } catch (Exception e) {
                log.error("Error checking task status from API: ${e.message}", e)

                // If the API call fails, return the current status from the database
                // with a warning message
                return [
                    success: true,
                    status: logEntry.status,
                    message: "Checking status... (API error: ${e.message})",
                    progress: 0,
                    startTime: logEntry.startTime,
                    endTime: logEntry.endTime,
                    duration: logEntry.endTime ? (logEntry.endTime.time - logEntry.startTime.time) / 1000 : null
                ]
            } finally {
                if (connection != null) {
                    connection.disconnect()
                }
            }

        } catch (Exception e) {
            log.error("Error getting task status: ${e.message}", e)
            return [success: false, error: e.message]
        }
    }

    def createMcqSolution(String sampleText, String questionText, List multipartFiles, def request) {

        String apiUrl = promptService.getGPTServerUrl(request) + "/create-solution"

        try {
            def http = new URL(apiUrl).openConnection() as HttpURLConnection
            def boundary = "----WebKitFormBoundary" + UUID.randomUUID().toString().replace("-", "")

            http.setRequestMethod("POST")
            http.setDoOutput(true)
            http.setRequestProperty("Content-Type", "multipart/form-data; boundary=${boundary}")

            def outputStream = http.getOutputStream()
            def writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true)

            // Add sample text if provided
            if (sampleText) {
                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"sample_text\"\r\n\r\n")
                writer.append("${sampleText}\r\n")
            }

            // Add question text if provided
            if (questionText) {
                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"question_text\"\r\n\r\n")
                writer.append("${questionText}\r\n")
            }

            // Add files
            multipartFiles.each { item ->
                def file = item.file
                def name = item.name

                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"${name}\"; filename=\"${file.originalFilename}\"\r\n")
                writer.append("Content-Type: ${file.contentType ?: 'application/octet-stream'}\r\n\r\n")
                writer.flush()

                // Write file data
                outputStream.write(file.bytes)
                outputStream.flush()

                writer.append("\r\n")
            }

            // End of multipart data
            writer.append("--${boundary}--\r\n")
            writer.flush()

            // Get response
            def status = http.getResponseCode()
            def response

            if (status == 200) {
                response = new JsonSlurper().parseText(http.getInputStream().getText())
            } else {
                throw new RuntimeException("API call failed with status ${status}: ${http.getErrorStream()?.getText()}")
            }

            return response
        } catch (Exception e) {
            log.error("Error in createMcqSolution: ${e.message}", e)
            return [success: false, message: "Error processing request: ${e.message}"]
        }
    }

    def extractAndValidateMcq(def request, String resId, def session) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/mcq-text-extractor"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)
            Integer siteId = utilService.getSiteId(request,session);
            // Create the request body
            def requestBody = [
                res_id: resId,
                from_ws: true,
                username: springSecurityService.currentUser ? springSecurityService.currentUser.username : siteId+"_8754178781"
            ]

            // Write the JSON body
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            def json = new JsonBuilder(requestBody)
            writer.write(json.toString())
            writer.flush()
            writer.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                    success: true,
                    data: jsonResponse
                ]
            } else {
                // Read the error response body to get more details
                def reader = new BufferedReader(new InputStreamReader(
                    responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                ))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                return [
                    success: false,
                    data: null,
                    message: "Error from server: ${responseCode}. Details: ${response.toString()}"
                ]
            }
        } catch (Exception e) {
            log.error("Error extracting and validating MCQs: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }
}
